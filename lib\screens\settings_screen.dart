// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:itravel/services/supabase_storage_service.dart';
import 'package:logger/logger.dart';
import 'welcome_screen.dart'; // Import WelcomeScreen instead of LoginScreen

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final Logger _logger = Logger(); // Initialize the logger
  final firebase_auth.FirebaseAuth _auth = firebase_auth.FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final SupabaseStorageService _supabaseStorageService =
      SupabaseStorageService();

  firebase_auth.User? _user;
  String? _profileImageUrl;

  @override
  void initState() {
    super.initState();
    _user = _auth.currentUser;
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    if (_user != null) {
      try {
        final docSnapshot =
            await _firestore.collection('users').doc(_user!.uid).get();
        if (docSnapshot.exists) {
          setState(() {
            _profileImageUrl = docSnapshot.data()?['avatar_url'];
          });
          _logger.i("User profile loaded successfully.");
        } else {
          _logger.w("User profile document not found.");
        }
      } catch (e) {
        _logger.e("Error loading user profile: $e");
      }
    }
  }

  Future<void> _updateProfilePicture() async {
    try {
      final pickedFile = await _supabaseStorageService.pickImage();
      if (pickedFile != null) {
        final downloadUrl =
            await _supabaseStorageService.uploadImage(pickedFile);
        if (downloadUrl != null) {
          setState(() {
            _profileImageUrl = downloadUrl;
          });

          final userDocRef = _firestore.collection('users').doc(_user?.uid);
          final docSnapshot = await userDocRef.get();
          if (docSnapshot.exists) {
            await userDocRef.update({'avatar_url': downloadUrl});
          } else {
            await userDocRef.set({
              'avatar_url': downloadUrl,
              'display_name': _user?.displayName ?? 'User',
              'created_at': FieldValue.serverTimestamp(),
            });
          }
          _logger.i("Profile picture updated successfully.");
        } else {
          _logger.w("Failed to upload image to Supabase storage.");
        }
      }
    } catch (e) {
      _logger.e("Error updating profile picture: $e");
    }
  }

  void _showTermsOfService() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Terms of Service'),
          content: SingleChildScrollView(
            child: Text(
              'Terms of Service\n\n'
              '1. Acceptance of Terms\n'
              'By using this app, you agree to these terms...\n\n'
              '2. Privacy\n'
              'Your data is securely managed according to our privacy policy.\n\n'
              '3. User Conduct\n'
              'You agree not to misuse the app...\n\n'
              '4. Modifications\n'
              'The app reserves the right to update terms at any time.\n\n',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _showAbout() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('About This App'),
          content: SingleChildScrollView(
            child: Text(
              'This app was made by Ilyes Saouak, an Information Systems Development student at ISET Tozeur. '
              'It was inspired by his love for traveling the world and the desire to have a tool to track and discover new destinations. '
              'This app helps users manage their travel bucket list, discover new places, and keep track of visited destinations.\n\n'
              'With continued updates, this app aims to be a reliable travel companion for adventurers everywhere. Enjoy discovering new destinations and keep track of your journeys!',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Close'),
            ),
          ],
        );
      },
    );
  }

  void _confirmLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Log Out'),
        content: Text('Are you sure you want to log out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              await _auth.signOut();
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (context) => WelcomeScreen()),
                (route) => false,
              );
              _logger.i("User logged out successfully.");
            },
            child: Text('Log Out'),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteAccount(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Account'),
        content: Text(
            'Are you sure you want to delete your account? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _reauthenticateAndDelete();
            },
            child: Text('Delete Account'),
          ),
        ],
      ),
    );
  }

  Future<void> _reauthenticateAndDelete() async {
    if (_user == null) return;
    String password = await _promptForPassword();

    if (password.isNotEmpty) {
      try {
        firebase_auth.AuthCredential credential =
            firebase_auth.EmailAuthProvider.credential(
          email: _user!.email!,
          password: password,
        );

        await _user!.reauthenticateWithCredential(credential);
        await _user!.delete();
        _logger.i("User account deleted successfully.");
        if (mounted) {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => WelcomeScreen()),
            (route) => false,
          );
        }
      } catch (e) {
        _logger.e("Error deleting user account: $e");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${e.toString()}')),
          );
        }
      }
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Reauthentication failed.')),
      );
    }
  }

  Future<String> _promptForPassword() async {
    String password = '';
    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Re-enter Password'),
          content: TextField(
            obscureText: true,
            decoration: InputDecoration(labelText: 'Password'),
            onChanged: (value) => password = value,
          ),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context), child: Text('OK'))
          ],
        );
      },
    );
    return password;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Settings',
          style: TextStyle(
              fontWeight: FontWeight.bold, fontSize: 24, color: Colors.white),
        ),
        backgroundColor: Color(0xFF23242F),
        automaticallyImplyLeading: false,
      ),
      backgroundColor: Color(0xFF1E1F28),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF1E1F28), Color(0xFF23242F)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundImage: _profileImageUrl != null
                        ? NetworkImage(_profileImageUrl!)
                        : AssetImage('assets/default_avatar.png')
                            as ImageProvider,
                  ),
                  SizedBox(height: 10),
                  Text(
                    _user?.displayName ?? 'Full Name',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 20),
                ],
              ),
            ),
            ListTile(
              leading: Icon(Icons.edit, color: Colors.white),
              title: Text('Change Profile Picture',
                  style: TextStyle(color: Colors.white)),
              onTap: _updateProfilePicture,
            ),
            ListTile(
              leading: Icon(Icons.article, color: Colors.white),
              title: Text('Terms of Service',
                  style: TextStyle(color: Colors.white)),
              onTap: _showTermsOfService,
            ),
            ListTile(
              leading: Icon(Icons.info, color: Colors.white),
              title: Text('About', style: TextStyle(color: Colors.white)),
              onTap: _showAbout,
            ),
            Divider(color: Colors.grey),
            Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () => _confirmLogout(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    padding: EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20)),
                  ),
                  child: Text(
                    'Log Out',
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => _confirmDeleteAccount(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    padding: EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20)),
                  ),
                  child: Text(
                    'Delete Account',
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

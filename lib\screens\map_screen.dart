// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:mapbox_gl/mapbox_gl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  _MapScreenState createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final Logger _logger = Logger(); // Initialize the logger
  MapboxMapController? _mapController;
  double _currentZoom = 2.0;
  final LatLng _initialPosition = LatLng(0, 0); // Center of the Earth
  final TextEditingController _searchController = TextEditingController();
  final String _mapboxAccessToken =
      "********************************************************************************************"; // Replace with your Mapbox token

  @override
  void initState() {
    super.initState();
    _requestLocationPermission();
  }

  Future<void> _requestLocationPermission() async {
    if (await Permission.location.isGranted) {
      _logger.i("Location permission already granted");
      return;
    }

    var status = await Permission.location.request();
    if (status.isPermanentlyDenied) {
      _logger.w("Location permission permanently denied");
      openAppSettings();
    } else if (status.isGranted) {
      _logger.i("Location permission granted");
    } else {
      _logger.w("Location permission denied");
    }
  }

  void _onMapCreated(MapboxMapController controller) {
    _logger.i("Map created successfully");
    _mapController = controller;
  }

  void _onCameraIdle() {
    if (_mapController != null && _mapController!.cameraPosition != null) {
      setState(() {
        _currentZoom = _mapController!.cameraPosition!.zoom;
      });
      _logger.d("Camera idle. Current zoom: $_currentZoom");
    }
  }

  Future<void> _searchPlace(String query) async {
    final url = Uri.parse(
        'https://api.mapbox.com/geocoding/v5/mapbox.places/$query.json?access_token=$_mapboxAccessToken');

    _logger.i("Searching for place: $query");

    try {
      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _logger.d("Search response data: $data");

        if (data['features'] != null && data['features'].isNotEmpty) {
          final feature = data['features'][0];
          final lat = feature['center'][1];
          final lng = feature['center'][0];
          _logger.i("Place found at: Lat=$lat, Lng=$lng");
          _moveToLocation(LatLng(lat, lng));
        } else {
          _logger.w("Location not found for query: $query");
          ScaffoldMessenger.of(context)
              .showSnackBar(SnackBar(content: Text("Location not found")));
        }
      } else {
        _logger
            .e("Error fetching location. Status code: ${response.statusCode}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Error fetching location")),
        );
      }
    } catch (e) {
      _logger.e("Exception during location search: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text("An error occurred while searching for location")),
      );
    }
  }

  void _moveToLocation(LatLng location) {
    if (_mapController != null) {
      _mapController?.animateCamera(
        CameraUpdate.newLatLngZoom(location, 12.0),
      );
      _logger.i("Moving camera to location: $location");
    } else {
      _logger.w("Map controller is null. Cannot move to location.");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Map',
          style: TextStyle(
              fontWeight: FontWeight.bold, fontSize: 24, color: Colors.white),
        ),
        backgroundColor: Color(0xFF23242F),
        automaticallyImplyLeading: false,
      ),
      body: Stack(
        children: [
          MapboxMap(
            accessToken: _mapboxAccessToken,
            initialCameraPosition: CameraPosition(
              target: _initialPosition,
              zoom: _currentZoom,
            ),
            onMapCreated: _onMapCreated,
            onCameraIdle: _onCameraIdle,
            styleString: MapboxStyles.MAPBOX_STREETS,
            myLocationEnabled: true,
            myLocationTrackingMode: MyLocationTrackingMode.Tracking,
            zoomGesturesEnabled: true,
          ),
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: "Search for a place...",
                  contentPadding: EdgeInsets.all(16),
                  border: InputBorder.none,
                  suffixIcon: IconButton(
                    icon: Icon(Icons.search),
                    onPressed: () {
                      if (_searchController.text.isNotEmpty) {
                        _searchPlace(_searchController.text);
                      }
                    },
                  ),
                ),
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    _searchPlace(value);
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

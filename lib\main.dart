import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart'
    as firebase_auth; // Alias firebase_auth
import 'package:supabase_flutter/supabase_flutter.dart';
import 'screens/welcome_screen.dart';
import 'screens/main_screen.dart';
import 'package:google_fonts/google_fonts.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Initialize Supabase
  await Supabase.initialize(
    url: 'https://dezwiueeydktpxcirjyw.supabase.co', // Supabase project URL
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRlendpdWVleWRrdHB4Y2lyanl3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzE3NjEwNDIsImV4cCI6MjA0NzMzNzA0Mn0.rYWDG1r1RYY9aq5jY3a5ekCv6BbTg_kDLh_Mcuf_0xA', // Supabase API key
  );

  runApp(TravelBucketListApp());
}

class TravelBucketListApp extends StatelessWidget {
  const TravelBucketListApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Travel Bucket List',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        textTheme: GoogleFonts.poppinsTextTheme(
          Theme.of(context).textTheme,
        ),
      ),
      debugShowCheckedModeBanner: false,
      home: AuthWrapper(), // Start with AuthWrapper to check auth state
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<firebase_auth.User?>(
      // Specify firebase_auth.User here
      stream: firebase_auth.FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // Check if the user is logged in
        if (snapshot.connectionState == ConnectionState.active) {
          final firebase_auth.User? user =
              snapshot.data; // Use firebase_auth.User here
          // If logged in, go to MainScreen; otherwise, go to WelcomeScreen
          return user == null ? WelcomeScreen() : MainScreen();
        }
        // Show loading screen while waiting for authentication state
        return Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      },
    );
  }
}

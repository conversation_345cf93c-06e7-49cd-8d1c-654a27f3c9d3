// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'explore_screen.dart';
import 'bucket_list_screen.dart';
import 'visited_screen.dart';
import 'map_screen.dart';
import 'settings_screen.dart';

class MainScreen extends StatefulWidget {
  final int initialIndex;

  const MainScreen({super.key, this.initialIndex = 0});

  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
  }

  final List<Widget> _screens = [
    ExploreScreen(),
    BucketListScreen(),
    VisitedScreen(),
    MapScreen(),
    SettingsScreen(),
  ];

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF1E1F28), Color(0xFF23242F)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              spreadRadius: 5,
              blurRadius: 10,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: BottomNavigationBar(
            backgroundColor: Colors.transparent,
            currentIndex: _currentIndex,
            onTap: _onTabTapped,
            type: BottomNavigationBarType.fixed,
            items: [
              BottomNavigationBarItem(
                icon: _buildIcon(Icons.explore, 'Explore', 0),
                label: '',
              ),
              BottomNavigationBarItem(
                icon: _buildIcon(Icons.list, 'Bucket List', 1),
                label: '',
              ),
              BottomNavigationBarItem(
                icon: _buildIcon(Icons.check_circle, 'Visited', 2),
                label: '',
              ),
              BottomNavigationBarItem(
                icon: _buildIcon(Icons.map, 'Map', 3),
                label: '',
              ),
              BottomNavigationBarItem(
                icon: _buildIcon(Icons.settings, 'Settings', 4),
                label: '',
              ),
            ],
            selectedItemColor: Colors.blueAccent,
            unselectedItemColor: Colors.white54,
            showUnselectedLabels: false,
          ),
        ),
      ),
    );
  }

  Widget _buildIcon(IconData icon, String label, int index) {
    bool isSelected = _currentIndex == index;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 28,
          color: isSelected ? Colors.blueAccent : Colors.white54,
        ),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.blueAccent : Colors.white54,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
        if (isSelected)
          Container(
            margin: EdgeInsets.only(top: 6),
            height: 4,
            width: 20,
            decoration: BoxDecoration(
              color: Colors.blueAccent,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
      ],
    );
  }
}

// ignore_for_file: use_build_context_synchronously, library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:itravel/services/firestore_service.dart';
import 'package:logger/logger.dart';
import 'main_screen.dart';
import 'destination_details_screen.dart';

class BucketListScreen extends StatefulWidget {
  const BucketListScreen({super.key});

  @override
  _BucketListScreenState createState() => _BucketListScreenState();
}

class _BucketListScreenState extends State<BucketListScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  final Logger _logger = Logger();
  List<Map<String, dynamic>> _bucketListItems = [];
  DateTime? _startDate;
  DateTime? _endDate;
  int _rating = 0;
  final TextEditingController _favoritePartController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadBucketList();
  }

  Future<void> _loadBucketList() async {
    try {
      List<Map<String, dynamic>> bucketList =
          await _firestoreService.getBucketList();
      if (mounted) {
        setState(() {
          _bucketListItems = bucketList;
        });
      }
    } catch (e) {
      _logger.e("Error loading bucket list: $e");
    }
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && mounted) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  Future<void> _showAddToVisitedDialog(Map<String, dynamic> destination) async {
    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text("Add to Visited"),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setDialogState) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    TextField(
                      controller: _favoritePartController,
                      decoration: InputDecoration(labelText: 'Favorite Part'),
                    ),
                    SizedBox(height: 8),
                    TextButton(
                      onPressed: () => _selectDateRange(context),
                      child: Text(
                        _startDate != null && _endDate != null
                            ? 'Visited from ${DateFormat('yyyy-MM-dd').format(_startDate!)} to ${DateFormat('yyyy-MM-dd').format(_endDate!)}'
                            : 'Select Date Range',
                      ),
                    ),
                    SizedBox(height: 8),
                    Text("Rating"),
                    Row(
                      children: List.generate(5, (index) {
                        return IconButton(
                          icon: Icon(
                            index < _rating ? Icons.star : Icons.star_border,
                            color: Colors.yellow,
                          ),
                          onPressed: () {
                            setDialogState(() {
                              _rating = index + 1;
                            });
                          },
                        );
                      }),
                    ),
                  ],
                ),
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                if (mounted) Navigator.pop(context);
              },
              child: Text("Cancel"),
            ),
            ElevatedButton(
              onPressed: () async {
                if (_startDate != null && _endDate != null) {
                  try {
                    await _firestoreService.addToVisitedList(
                      destinationId: destination["destinationId"],
                      name: destination["name"],
                      imageUrl: destination["image"],
                      description: destination["description"],
                      location: destination["location"],
                      activities: List<String>.from(destination["activities"]),
                      startDate: _startDate!,
                      endDate: _endDate!,
                      rating: _rating,
                      favoritePart: _favoritePartController.text,
                    );

                    await _firestoreService
                        .removeFromBucketList(destination["destinationId"]);

                    if (mounted) {
                      _loadBucketList();
                      Navigator.pop(context);
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text("Error: $e")),
                      );
                    }
                  }
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Please select a date range')),
                    );
                  }
                }
              },
              child: Text("Add to Visited"),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteFromBucketList(String destinationId) async {
    try {
      await _firestoreService.removeFromBucketList(destinationId);
      if (mounted) {
        _loadBucketList();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Destination removed from bucket list")),
        );
      }
    } catch (e) {
      if (mounted) {
        _logger.e("Error removing destination: $e");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Error removing destination")),
        );
      }
    }
  }

  @override
  void dispose() {
    _favoritePartController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'My Bucket List',
          style: TextStyle(
              fontWeight: FontWeight.bold, fontSize: 24, color: Colors.white),
        ),
        backgroundColor: Color(0xFF23242F),
        automaticallyImplyLeading: false,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF1E1F28), Color(0xFF23242F)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'My Dream Destinations',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            Expanded(
              child: _bucketListItems.isEmpty
                  ? Center(
                      child: Text(
                        'No destinations added yet!',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.white70,
                        ),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _bucketListItems.length,
                      itemBuilder: (context, index) {
                        final destination = _bucketListItems[index];
                        return Card(
                          color: Color(0xFF2D2E37),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ListTile(
                            contentPadding: EdgeInsets.all(12),
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      DestinationDetailsScreen(
                                          name: destination["name"],
                                          imageUrl: destination["image"],
                                          description:
                                              destination["description"],
                                          location: destination["location"],
                                          activities: List<String>.from(
                                              destination["activities"]),
                                          addedBy: destination["addedBy"]),
                                ),
                              );
                            },
                            leading: Image.network(
                              destination["image"],
                              width: 80,
                              fit: BoxFit.cover,
                            ),
                            title: Text(
                              destination["name"],
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  destination["location"],
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white70,
                                  ),
                                ),
                                Text(
                                  "Added by ${destination["addedBy"]}",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.white60,
                                  ),
                                ),
                              ],
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: Icon(Icons.bookmark_added,
                                      color: Colors.blueAccent),
                                  onPressed: () =>
                                      _showAddToVisitedDialog(destination),
                                ),
                                IconButton(
                                  icon: Icon(Icons.delete, color: Colors.red),
                                  onPressed: () => _deleteFromBucketList(
                                      destination["destinationId"]),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => MainScreen(initialIndex: 0),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blueAccent,
                  padding: EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: Text(
                  'Add New Destination',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

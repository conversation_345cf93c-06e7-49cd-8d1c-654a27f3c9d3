import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';
import 'supabase_storage_service.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final SupabaseStorageService _supabaseStorageService =
      SupabaseStorageService();
  final Logger _logger = Logger(); // Initialize the logger

  // Method to create or update a user profile
  Future<void> createOrUpdateUserProfile(
      String fullName, File? profileImageFile) async {
    final user = _auth.currentUser;

    if (user != null) {
      try {
        // Upload the profile image to Supabase if provided
        String? profileImageUrl;
        if (profileImageFile != null) {
          profileImageUrl =
              await _supabaseStorageService.uploadImage(profileImageFile);
        }

        // Set user profile information in Firestore
        final userDoc = _firestore.collection('users').doc(user.uid);

        await userDoc.set({
          'fullName': fullName,
          'email': user.email,
          'profileImageUrl': profileImageUrl ?? '',
        }, SetOptions(merge: true));

        _logger.i("User profile created/updated successfully in Firestore.");
      } catch (e) {
        _logger.e("Failed to create/update user profile: $e");
      }
    } else {
      _logger.w("No authenticated user found while creating/updating profile.");
    }
  }

  // Method to initialize a user profile in Firestore after registration
  Future<void> addNewUserProfileAfterRegistration(String fullName) async {
    final user = _auth.currentUser;
    if (user != null) {
      try {
        final userDoc = _firestore.collection('users').doc(user.uid);
        final docSnapshot = await userDoc.get();

        // Create profile if it doesn't exist
        if (!docSnapshot.exists) {
          await userDoc.set({
            'fullName': fullName,
            'email': user.email,
            'profileImageUrl': '', // Default or blank if not set initially
          });
          _logger
              .i("New user profile created in Firestore after registration.");
        }
      } catch (e) {
        _logger.e("Error creating new user profile: $e");
      }
    } else {
      _logger.w("No authenticated user found during profile registration.");
    }
  }

  // Method to get user profile data
  Future<Map<String, dynamic>?> getUserProfile() async {
    final user = _auth.currentUser;
    if (user != null) {
      try {
        final doc = await _firestore.collection('users').doc(user.uid).get();
        return doc.data();
      } catch (e) {
        _logger.e("Error fetching user profile: $e");
      }
    }
    return null;
  }

  // Method to upload an image to Supabase and get its URL
  Future<String?> uploadImage(File imageFile) async {
    return await _supabaseStorageService.uploadImage(imageFile);
  }

  // Method to add a destination to Firestore
  Future<void> addDestination({
    required String name,
    required String imageUrl,
    required String description,
    required String location,
    required List<String> activities,
  }) async {
    CollectionReference destinations = _firestore.collection('destinations');

    final user = _auth.currentUser;
    if (user == null) {
      _logger.w("User not authenticated. Cannot add destination.");
      return;
    }

    try {
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (!userDoc.exists) {
        _logger.w("User document does not exist in Firestore.");
        return;
      }

      final addedBy = userDoc.data()?['fullName'] ?? 'Unknown User';

      Map<String, dynamic> destinationData = {
        "name": name,
        "image": imageUrl,
        "description": description,
        "location": location,
        "activities":
            activities.isNotEmpty ? activities : ["No activities available"],
        "addedBy": addedBy,
        "createdAt": FieldValue.serverTimestamp(),
      };

      await destinations.add(destinationData);
      _logger.i("Destination added successfully to Firestore.");
    } catch (e) {
      _logger.e("Failed to add destination to Firestore: $e");
    }
  }

  // Method to get all destinations from Firestore
  Future<List<Map<String, dynamic>>> getDestinations() async {
    try {
      CollectionReference destinations = _firestore.collection('destinations');
      QuerySnapshot snapshot = await destinations.get();

      return snapshot.docs
          .map((doc) => doc.data() as Map<String, dynamic>)
          .toList();
    } catch (e) {
      _logger.e("Error getting destinations: $e");
      return [];
    }
  }

  // Method to add a destination to the current user's bucket list
  Future<void> addToBucketList({
    required String destinationId,
    required String name,
    required String imageUrl,
    required String description,
    required String location,
    required List<String> activities,
  }) async {
    final String userId = _auth.currentUser!.uid;
    CollectionReference bucketList = _firestore.collection('bucket_list');

    Map<String, dynamic> bucketListItem = {
      "userId": userId,
      "destinationId": destinationId,
      "name": name,
      "image": imageUrl,
      "description": description,
      "location": location,
      "activities": activities,
      "addedAt": FieldValue.serverTimestamp(),
    };

    try {
      await bucketList.add(bucketListItem);
      _logger.i("Added to bucket list successfully.");
    } catch (e) {
      _logger.e("Failed to add to bucket list: $e");
    }
  }

  // Method to get the current user's bucket list items
  Future<List<Map<String, dynamic>>> getBucketList() async {
    final String userId = _auth.currentUser!.uid;
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('bucket_list')
          .where('userId', isEqualTo: userId)
          .get();

      return snapshot.docs
          .map((doc) => doc.data() as Map<String, dynamic>)
          .toList();
    } catch (e) {
      _logger.e("Error getting bucket list: $e");
      return [];
    }
  }

  // Method to add a destination to the current user's visited list with additional details
  Future<void> addToVisitedList({
    required String destinationId,
    required String name,
    required String imageUrl,
    required String description,
    required String location,
    required List<String> activities,
    required DateTime startDate,
    required DateTime endDate,
    required int rating,
    required String favoritePart,
  }) async {
    final String userId = _auth.currentUser!.uid;
    CollectionReference visitedList = _firestore.collection('visited');

    Map<String, dynamic> visitedListItem = {
      "userId": userId,
      "destinationId": destinationId,
      "name": name,
      "image": imageUrl,
      "description": description,
      "location": location,
      "activities": activities,
      "startDate": startDate,
      "endDate": endDate,
      "rating": rating,
      "favoritePart": favoritePart,
      "visitedAt": FieldValue.serverTimestamp(),
    };

    try {
      await visitedList.add(visitedListItem);
      _logger.i("Added to visited list successfully.");
    } catch (e) {
      _logger.e("Failed to add to visited list: $e");
    }
  }

  // Method to get the current user's visited destinations
  Future<List<Map<String, dynamic>>> getVisitedList() async {
    final String userId = _auth.currentUser!.uid;
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('visited')
          .where('userId', isEqualTo: userId)
          .get();

      return snapshot.docs
          .map((doc) => doc.data() as Map<String, dynamic>)
          .toList();
    } catch (e) {
      _logger.e("Error getting visited list: $e");
      return [];
    }
  }

  // Method to remove a destination from the bucket list
  Future<void> removeFromBucketList(String destinationId) async {
    final String userId = _auth.currentUser!.uid;
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('bucket_list')
          .where('userId', isEqualTo: userId)
          .where('destinationId', isEqualTo: destinationId)
          .get();

      for (var doc in snapshot.docs) {
        await doc.reference.delete();
      }
      _logger.i("Removed from bucket list successfully.");
    } catch (e) {
      _logger.e("Failed to remove from bucket list: $e");
    }
  }

  // Method to remove a destination from the visited list
  Future<void> removeFromVisitedList(String destinationId) async {
    final String userId = _auth.currentUser!.uid;
    try {
      QuerySnapshot snapshot = await _firestore
          .collection('visited')
          .where('userId', isEqualTo: userId)
          .where('destinationId', isEqualTo: destinationId)
          .get();

      for (var doc in snapshot.docs) {
        await doc.reference.delete();
      }
      _logger.i("Removed from visited list successfully.");
    } catch (e) {
      _logger.e("Failed to remove from visited list: $e");
    }
  }
}

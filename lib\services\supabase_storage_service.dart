import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:path/path.dart';
import 'package:logger/logger.dart';

class SupabaseStorageService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final ImagePicker _picker = ImagePicker();
  final Logger _logger = Logger(); // Initialize the logger

  // Function to pick an image from the gallery
  Future<File?> pickImage() async {
    try {
      final XFile? pickedFile =
          await _picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        _logger.i("Image picked successfully: ${pickedFile.path}");
        return File(pickedFile.path);
      } else {
        _logger.w("No image selected.");
        return null;
      }
    } catch (e) {
      _logger.e("Error picking image: $e");
      return null;
    }
  }

  // Function to upload an image to Supabase storage
  Future<String?> uploadImage(File imageFile) async {
    try {
      final String fileName = basename(imageFile.path);

      // Upload the file to Supabase storage
      await _supabase.storage
          .from('profile_pictures')
          .upload('public/$fileName', imageFile);

      // Get the public URL of the uploaded file
      final String publicURL = _supabase.storage
          .from('profile_pictures')
          .getPublicUrl('public/$fileName');

      _logger.i("Image uploaded successfully. Public URL: $publicURL");

      return publicURL;
    } catch (e) {
      _logger.e("Error uploading image to Supabase: $e");
      return null;
    }
  }
}

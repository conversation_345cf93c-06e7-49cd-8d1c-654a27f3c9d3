// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:itravel/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

class VisitedScreen extends StatefulWidget {
  const VisitedScreen({super.key});

  @override
  _VisitedScreenState createState() => _VisitedScreenState();
}

class _VisitedScreenState extends State<VisitedScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  final Logger _logger = Logger(); // Initialize the logger
  List<Map<String, dynamic>> _visitedDestinations = [];

  @override
  void initState() {
    super.initState();
    _loadVisitedDestinations();
  }

  Future<void> _loadVisitedDestinations() async {
    try {
      List<Map<String, dynamic>> visitedList =
          await _firestoreService.getVisitedList();
      if (mounted) {
        setState(() {
          _visitedDestinations = visitedList;
        });
      }
      _logger.i("Visited destinations loaded successfully.");
    } catch (e) {
      _logger.e("Error loading visited destinations: $e");
    }
  }

  Future<void> _removeFromVisited(String destinationId) async {
    try {
      await _firestoreService.removeFromVisitedList(destinationId);
      if (mounted) {
        _loadVisitedDestinations(); // Refresh the visited list after deletion
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Destination removed from visited list")),
        );
      }
      _logger.i("Destination removed from visited list: $destinationId");
    } catch (e) {
      _logger.e("Error removing destination: $e");
    }
  }

  void _showDestinationDetails(Map<String, dynamic> destination) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          backgroundColor: Color(0xFF2D2E37),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  destination["name"],
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 10),
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    destination["image"],
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: 200,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  "Location: ${destination["location"]}",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
                SizedBox(height: 4),
                if (destination["startDate"] != null &&
                    destination["endDate"] != null)
                  Text(
                    "Visited from ${DateFormat('yyyy-MM-dd').format((destination["startDate"] as Timestamp).toDate())} "
                    "to ${DateFormat('yyyy-MM-dd').format((destination["endDate"] as Timestamp).toDate())}",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                SizedBox(height: 4),
                if (destination["rating"] != null)
                  Text(
                    "Rating: ${destination["rating"]}/5",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.amber,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                SizedBox(height: 4),
                if (destination["favoritePart"] != null)
                  Text(
                    "Favorite Part: ${destination["favoritePart"]}",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        'Close',
                        style:
                            TextStyle(color: Colors.blueAccent, fontSize: 16),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        _removeFromVisited(destination["destinationId"]);
                        Navigator.pop(context);
                      },
                      child: Text(
                        'Delete',
                        style: TextStyle(color: Colors.red, fontSize: 16),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFF1E1F28),
      appBar: AppBar(
        title: Text(
          'Visited Destinations',
          style: TextStyle(
              fontWeight: FontWeight.bold, fontSize: 24, color: Colors.white),
        ),
        backgroundColor: Color(0xFF23242F),
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF1E1F28), Color(0xFF1E1F28)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          padding: EdgeInsets.all(16.0),
          child: _visitedDestinations.isEmpty
              ? Center(
                  child: Text(
                    'No destinations visited yet!',
                    style: TextStyle(fontSize: 18, color: Colors.white70),
                  ),
                )
              : ListView.builder(
                  itemCount: _visitedDestinations.length,
                  itemBuilder: (context, index) {
                    final destination = _visitedDestinations[index];
                    return Card(
                      color: Color(0xFF2D2E37),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      margin: EdgeInsets.symmetric(vertical: 8),
                      child: ListTile(
                        contentPadding: EdgeInsets.all(12),
                        leading: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            destination["image"],
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                          ),
                        ),
                        title: Text(
                          destination["name"],
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        subtitle: Text(
                          destination["location"],
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white70,
                          ),
                        ),
                        trailing: IconButton(
                          icon: Icon(Icons.delete, color: Colors.red),
                          onPressed: () {
                            _removeFromVisited(destination["destinationId"]);
                          },
                        ),
                        onTap: () => _showDestinationDetails(destination),
                      ),
                    );
                  },
                ),
        ),
      ),
    );
  }
}

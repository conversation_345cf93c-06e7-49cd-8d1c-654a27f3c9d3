plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"

    // Google services Gradle plugin
    id "com.google.gms.google-services"
}

android {
    namespace = "com.elyesdev.itravel"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId = "com.elyesdev.itravel"
        minSdkVersion 23
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutter.versionCode
        versionName flutter.versionName
    }

    signingConfigs {
        release {
            def keystoreProperties = new Properties()
            def keystoreFile = rootProject.file("key.properties")
            keystoreProperties.load(new FileInputStream(keystoreFile))

            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Firebase BoM to manage versions
    implementation platform('com.google.firebase:firebase-bom:33.5.1')

    // Firebase dependencies
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-firestore'
}

// Apply Google services plugin
apply plugin: 'com.google.gms.google-services'

// Handle missing namespaces for libraries
subprojects {
    afterEvaluate { project ->
        if (project.plugins.hasPlugin("com.android.library")) {
            project.android {
                if (namespace == null) {
                    namespace = project.group.toString()
                }
            }
        }
    }
}
